from .utils import proxy, gen_session


def get_house_value(addy):
    try:
        data = None
        for i in range(10):
            try:
                session, user_agent = gen_session()
                session.proxies = {"http": proxy, "https": proxy}

                response = session.get(
                    f"https://www.zillowstatic.com/autocomplete/v3/suggestions/?q={addy.replace(' ', '+').replace('.', '').replace('#', '').replace(',', '')}&resultTypes=allAddress&resultCount=5",
                    headers={
                        'authority': 'www.zillow.com',
                        'accept': '*/*',
                        'accept-language': 'en-GB,en;q=0.9',
                        'client-id': 'hmimhw-property-search', 
                        'content-type': 'application/json',
                        'origin': 'https://www.zillow.com',
                        'referer': 'https://www.zillow.com/how-much-is-my-home-worth/',
                        'user-agent': user_agent,
                    },
                    #proxy=proxy
                )

                zpid = response.json()["results"][0]["metaData"]["zpid"]
                data = {"operationName":"HowMuchIsMyHomeWorthReviewQuery","variables":{"zpid":zpid},"query":"query HowMuchIsMyHomeWorthReviewQuery($zpid: ID!) {\n  property(zpid: $zpid) {\n    streetAddress\n    city\n    state\n    zipcode\n    bedrooms\n    bathrooms\n    livingArea\n    zestimate\n    homeStatus\n    photos(size: XL) {\n      url\n      __typename\n    }\n    ...OmpHomeWorthUpsell_property\n    isConfirmedClaimedByCurrentSignedInUser\n    isVerifiedClaimedByCurrentSignedInUser\n    ...UARequiredPropertyDimensions_property\n    ...ContactAgentForm_property\n    ...HomeInfo_property\n    __typename\n  }\n  viewer {\n    ...ContactAgentForm_viewer\n    __typename\n  }\n  abTests {\n    ...OmpHomeWorthUpsell_abTests\n    ...UARequiredPropertyDimensions_abTests\n    ...ContactAgentForm_abTests\n    __typename\n  }\n}\n\nfragment OmpHomeWorthUpsell_property on Property {\n  zpid\n  onsiteMessage(placementNames: [\"HMIMHWTopSlot\"]) {\n    ...onsiteMessage_fragment\n    __typename\n  }\n  __typename\n}\n\nfragment onsiteMessage_fragment on OnsiteMessageResultType {\n  eventId\n  decisionContext\n  messages {\n    skipDisplayReason\n    shouldDisplay\n    isGlobalHoldout\n    isPlacementHoldout\n    placementName\n    testPhase\n    bucket\n    placementId\n    passThrottle\n    lastModified\n    eventId\n    decisionContext\n    selectedTreatment {\n      id\n      name\n      component\n      status\n      renderingProps\n      lastModified\n      __typename\n    }\n    qualifiedTreatments {\n      id\n      name\n      status\n      lastModified\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n\nfragment OmpHomeWorthUpsell_abTests on ABTests {\n  HMIMHW_ZO_NFS_UPSELL_ONSITE_MESSAGING: abTest(\n    trial: \"HMIMHW_ZO_NFS_UPSELL_ONSITE_MESSAGING\"\n  )\n  __typename\n}\n\nfragment UARequiredPropertyDimensions_property on Property {\n  currency\n  featuredListingTypeDimension\n  hasPublicVideo\n  hdpTypeDimension\n  listingTypeDimension\n  price\n  propertyTypeDimension\n  standingOffer {\n    isStandingOfferEligible\n    __typename\n  }\n  zpid\n  isZillowOwned\n  zillowOfferMarket {\n    legacyName\n    __typename\n  }\n  ...ShouldShowVideo_property\n  __typename\n}\n\nfragment ShouldShowVideo_property on Property {\n  zpid\n  __typename\n}\n\nfragment UARequiredPropertyDimensions_abTests on ABTests {\n  ZO_HDP_HOUR_ONE_VIDEO: abTest(trial: \"ZO_HDP_HOUR_ONE_VIDEO\")\n  __typename\n}\n\nfragment ContactAgentForm_property on Property {\n  streetAddress\n  state\n  city\n  zipcode\n  zpid\n  homeStatus\n  homeType\n  zestimate\n  homeType\n  isInstantOfferEnabled\n  zillowOfferMarket {\n    name\n    code\n    __typename\n  }\n  __typename\n}\n\nfragment ContactAgentForm_viewer on Viewer {\n  name\n  email\n  zuid\n  __typename\n}\n\nfragment ContactAgentForm_abTests on ABTests {\n  SHOW_PL_LEAD_FORM: abTest(trial: \"SHOW_PL_LEAD_FORM\")\n  __typename\n}\n\nfragment HomeInfo_property on Property {\n  streetAddress\n  city\n  state\n  zipcode\n  bedrooms\n  bathrooms\n  livingArea\n  homeStatus\n  homeType\n  contingentListingType\n  photos(size: XL) {\n    url\n    __typename\n  }\n  listing_sub_type {\n    is_newHome\n    is_FSBO\n    is_bankOwned\n    is_foreclosure\n    is_forAuction\n    is_comingSoon\n    __typename\n  }\n  __typename\n}\n"}

                break
            except Exception as e:
                pass
        
        if not zpid:
            raise Exception

        for i in range(10):
            try:
                session, user_agent = gen_session()
                session.proxies = {"http": proxy, "https": proxy}

                response = session.post(
                    'https://www.zillow.com/graphql/',  
                    headers={
                        'authority': 'www.zillow.com',
                        'Client-Id': 'hmimhw-property-search',
                        'accept': '*/*',
                        'accept-language': 'en-GB,en;q=0.9',
                        'client-id': 'hmimhw-property-search', 
                        'content-type': 'application/json',
                        'origin': 'https://www.zillow.com',
                        'referer': 'https://www.zillow.com/how-much-is-my-home-worth/',
                        'user-agent': user_agent,
                    }, 
                    json=data,
                    allow_redirects=False
                    #proxy=proxy
                )

                if 'Before we continue...' not in response.text:
                    try:
                        estimate = response.json()["data"]["property"]["zestimate"]
                        if estimate == None:return estimate
                        value = "{:,}$".format(estimate)
                    except:
                        value = None
                    return value

            except Exception as e:
                pass
    except Exception as e:
        return None