from colorama import Fore
import re, threading, json, random, time
from curl_cffi import requests
from bs4 import BeautifulSoup

config = json.load(open('./config.json'))
proxy = config['proxy']
lock = threading.Lock()

def s_print(content,color=Fore.WHITE):
  global lock
  lock.acquire()
  print(f"{color} > {Fore.WHITE}{content}")
  lock.release()
  
def extractNumb(number):
    clean_number = re.sub(r'\D', '', number)
    return clean_number

def read_txt(filename):
    with open(filename, "r", errors='ignore') as f:
        phones = f.readlines()
    return [phone.strip() for phone in phones]

def gen_session():
    spoofable = [
        "safari17_2_ios",
        "safari17_0",
        "safari_ios",
        "chrome124",
        "chrome123",
        "safari15_5"
    ]

    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.1",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Safari/605.1.1",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/129.0.0.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:130.0) Gecko/20100101 Firefox/130.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36 Edg/125.0.0.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/117.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.3",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.3",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 Edg/124.0.0."
    ]

    session = requests.Session(impersonate=random.choice(spoofable))
    user_agent = random.choice(user_agents)

    return session, user_agent

def numerify(string: str) -> str:
    result = ''
    for char in string:
        if char in '1234567890':
            result += char
    return result 

def number_match_check(unconf_nums, conf_nums):
    full_conf_nums = set()

    if conf_nums is None:
        return []

    for conf_num in conf_nums:
        for unconf_num in unconf_nums:
            try:
                if 'XX' in unconf_num or "**" in unconf_num or '??' in unconf_num or 'None' in unconf_num or 'null' in unconf_num or 'Null' in unconf_num:
                    continue

                unconf_num = numerify(unconf_num)

                if not unconf_num.endswith(conf_num[-2:]):
                    continue

                if unconf_num.startswith('+44'):
                    unconf_num = unconf_num[3:]

                if unconf_num.startswith('1') and len(unconf_num) == 10:
                    unconf_num = unconf_num[1:]

                if unconf_num.startswith('+1'):
                    unconf_num = unconf_num[2:]

                full_conf_nums.add(unconf_num)
            except: pass

    return list(full_conf_nums)

def format_number(number):
    try:
        last_four_digits = number[-4:]
        return last_four_digits
    except:
        return None

def save(content, filename):
    """Save content to file with thread safety"""
    global lock
    lock.acquire()
    try:
        with open(filename, 'a', encoding='utf-8') as f:
            f.write(content + '\n')
    except Exception as e:
        s_print(f"Error saving to {filename}: {e}", Fore.RED)
    finally:
        lock.release()

def read_csv(filename):
    """Read CSV file and return list of dictionaries"""
    import csv
    try:
        with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
            reader = csv.DictReader(f)
            return list(reader)
    except Exception as e:
        s_print(f"Error reading CSV {filename}: {e}", Fore.RED)
        return []

def format_phone_for_comparison(phone):
    """Format phone number for comparison by removing all non-digits"""
    if not phone:
        return ""
    return re.sub(r'\D', '', str(phone))

def extract_phone_numbers(text):
    """Extract phone numbers from text using regex"""
    if not text:
        return []

    # Pattern to match various phone number formats
    phone_patterns = [
        r'\(\d{3}\)\s*\d{3}-\d{4}',  # (*************
        r'\d{3}-\d{3}-\d{4}',        # ************
        r'\d{3}\.\d{3}\.\d{4}',      # ************
        r'\d{3}\s+\d{3}\s+\d{4}',    # ************
        r'\d{10}',                   # 1234567890
        r'\+1\d{10}',                # +11234567890
        r'\+\d{1,3}\d{10}',          # +11234567890 or similar
    ]

    phones = []
    for pattern in phone_patterns:
        matches = re.findall(pattern, str(text))
        phones.extend(matches)

    # Clean and format found numbers
    cleaned_phones = []
    for phone in phones:
        cleaned = format_phone_for_comparison(phone)
        if len(cleaned) >= 10:  # Valid phone number length
            cleaned_phones.append(cleaned)

    return list(set(cleaned_phones))  # Remove duplicates

def phone_number_match(csv_phones, hint_phones):
    """Check if any phone numbers from CSV match any from hints"""
    if not csv_phones or not hint_phones:
        return []

    # Convert CSV phones to list if it's a string
    if isinstance(csv_phones, str):
        csv_phone_list = [p.strip() for p in csv_phones.split(',') if p.strip()]
    else:
        csv_phone_list = csv_phones if isinstance(csv_phones, list) else [csv_phones]

    # Format all CSV phones for comparison
    formatted_csv_phones = []
    for phone in csv_phone_list:
        formatted = format_phone_for_comparison(phone)
        if formatted:
            formatted_csv_phones.append(formatted)

    # Extract and format hint phones
    formatted_hint_phones = []
    for hint in hint_phones:
        extracted = extract_phone_numbers(hint)
        formatted_hint_phones.extend(extracted)

    # Find matches
    matches = []
    for csv_phone in formatted_csv_phones:
        for hint_phone in formatted_hint_phones:
            # Check if last 4 digits match (common for partial phone reveals)
            if len(csv_phone) >= 4 and len(hint_phone) >= 4:
                if csv_phone[-4:] == hint_phone[-4:]:
                    matches.append(csv_phone)
                # Also check full number match
                elif csv_phone == hint_phone:
                    matches.append(csv_phone)

    return list(set(matches))  # Remove duplicates