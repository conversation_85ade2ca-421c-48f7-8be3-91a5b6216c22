import tls_client
from colorama import init, Fore
from .utils import proxy, lock, s_print, gen_session

def scrape_twitter(email: str): 
    session, user_agent = gen_session()
    session.proxies.update({
        'https': proxy,
        'http': proxy,
    })

    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en,en-US;q=0,5',
        'Cache-Control': 'max-age=0',
        'Referer': 'https://www.bing.com/',
        'Sec-Ch-Ua': '"Chromium";v="118", "Brave";v="118", "Not=A?Brand";v="99"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Fetch-User': '?1',
        'Sec-Gpc': '1',
        #'Upgrade-Insecure-Requests': '1',
        'User-Agent': user_agent
    }

    r = session.get('https://twitter.com/account/begin_password_reset', headers=headers)

    authenticity_token = r.text.split('<input type="hidden" name="authenticity_token" value="')[1].split('"')[0]

    headers['Referer'] = 'https://twitter.com/account/begin_password_reset'
    r2 = session.post('https://twitter.com/account/begin_password_reset', headers=headers, data = {
        'authenticity_token': authenticity_token,
        'account_identifier': email
    }, allow_redirects=True)

    if "We couldn't find your account with that information" in r2.text:
        #s_print(f'{email} - Account not found', Fore.RED)
        return []

    if "You've exceeded the number of attempts. Please try again later." in r2.text:
        #s_print(f'{email} - Rate Limited', Fore.YELLOW)
        return []

    if ("Send an email to" in r2.text and "Text a code to the phone number" not in r2.text):
        email_hint = r2.text.split('Send an email to <strong dir="ltr">')[1].split('</')[0]
        #s_print(f'{email} | Phone: null | Email: {email_hint}', Fore.GREEN)
        return []

    elif ("Send an email to" not in r2.text and "Text a code to the phone number" in r2.text):
        phone_hint = r2.text.split('Text a code to the phone number ending in <strong dir="ltr">')[1].split('</')[0]
        #s_print(f'{email} | Phone: *{phone_hint} | Email: null', Fore.GREEN)
        return [phone_hint]

    try:
        phone_hint = r2.text.split('Text a code to the phone number ending in <strong dir="ltr">')[1].split('</')[0]
    except:
        return []
    
    #s_print(f'{email} | Phone: *{phone_hint}', Fore.GREEN)

    return [phone_hint]