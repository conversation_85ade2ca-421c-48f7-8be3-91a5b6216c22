import tls_client, re
from .utils import proxy, lock, s_print

def decode_url(url):
    decoded_url = url.replace(r"\u00253A", "%3A").replace(r"\u00252F", "%2F")
    return decoded_url

def scrape_facebook(email):
    try:
        session = tls_client.Session(
            client_identifier="firefox110",
            random_tls_extension_order=True
        )

        session.proxies={
            'https': proxy,
            'http': proxy
        }

        res0 = session.get(
            "https://www.facebook.com/login/identify/?ctx=recover&ars=facebook_login&next=https://www.facebook.com/&from_login_screen=0",
            headers={
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'en-US,en;q=0.9',
                'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 9; Redmi 7 MIUI/V11.0.6.0.PFLMIXM) [FBAN/MessengerLite;FBAV/*********.114;FBPN/com.facebook.mlite;FBLC/ar_EG;FBBV/257412622;FBCR/Orange - STAY SAFE;FBMF/Xiaomi;FBBD/xiaomi;FBDV/Redmi 7;FBSV/9;FBCA/arm64-v8a:null;FBDM/{density=2.0,width=720,height=1369};]'
            },
            allow_redirects=True
        )
        datr = re.findall(r'"_js_datr","([^"]+)",', res0.text)[0]
        
        jazoest = re.findall(r'<input type="hidden" name="jazoest" value="([^"]+)" autocomplete="off" />', res0.text)
        lsd = re.findall(r'<input type="hidden" name="lsd" value="([^"]+)" autocomplete="off" />', res0.text)
        __hs = re.findall(r'"haste_session":"([^"]+)"', res0.text)
        __ccg = re.findall(r'{"connectionClass":"([^"]+)"}', res0.text)
        __rev = re.findall(r'{"rev":([^"]+)}', res0.text)
        __hsi = re.findall(r'"hsi":"([^"]+)"', res0.text)
        __spin_r = re.findall(r'"__spin_r":([^"]+),', res0.text)
        __spin_b = re.findall(r'"__spin_b":"([^"]+)"', res0.text)
        __spin_t = re.findall(r'"__spin_t":([^"]+),', res0.text)


        res1 = session.post(
            f'https://www.facebook.com/ajax/login/help/identify.php?next=https://www.facebook.com/&ctx=recover',
            headers={
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Cookie': f'datr={datr}; wd=1600x900',
                'Host': 'www.facebook.com',
                'Origin': 'https://www.facebook.com',
                'Referer': 'https://www.facebook.com/login/identify/?ctx=recover&ars=facebook_login&next=https://www.facebook.com/&from_login_screen=0',
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                'X-ASBD-ID': '129477',
                'X-FB-LSD': str(lsd[0])
            },
            data={
                'jazoest': str(jazoest[0]),
                'lsd': str(lsd[0]),
                'email': str(email),
                'did_submit': '1',
                '__user': '0',
                '__a': '1',
                '__req': '4',
                '__hs': str(__hs[0]),
                'dpr': '1',
                '__ccg': str(__ccg[0]),
                '__rev': str(__rev[0]),
                '__s': 'hnxfuh:qxjmwx:w2kqh2',
                '__hsi': str(__hsi[0]),
                '__dyn': '7xeUmwkHg7ebwKBAg5S1Dxu13wqovzEdEc8uxa0CEbo1nEhwem0nCq1ewcG0KEswaq0yE7i0n24o5-0ha2l0Fwqo31w9O0H8jwae4Ueo2swwwNwmE2ewnE2Lx-0lK3qazo11E2ZwrU6C0hq1Iw7zwtU5K',
                '__csr': '',
                '__spin_r': str(__spin_r[0]),
                '__spin_b': str(__spin_b[0]),
                '__spin_t': str(__spin_t[0])
            },
            allow_redirects=True
        )
        try:
            sfiu = res1.cookies.get('sfiu')
            redir_url = re.findall(r'"([^"]+)",true,false]]]}', res1.text)[0]
            redir_url =  'https://www.facebook.com' + str(decode_url(redir_url).replace('\\', ''))
            _cookies={
                'datr': datr,
                'sfiu': sfiu
            }

            redir_link = f'https://www.facebook.com/recover/initiate/?is_from_lara_screen=1&next=https%3A%2F%2Fwww.facebook.com%2F'
            recov = session.get(
                f'{redir_link}',
                headers={
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                    'Accept': '*/*',
                    'Referer': 'https://www.facebook.com/login/identify/?ctx=recover&ars=facebook_login&next=https%3A%2F%2Fwww.facebook.com%2F&from_login_screen=0',
                    'Cookie': f'datr={datr}; sfiu={sfiu}',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'en-US,en;q=0.9',
                },
                cookies=_cookies,
                allow_redirects=True
            )
            phone_hint = re.findall(r'\+[*]+\d{2}', recov.text)
            return phone_hint
        except Exception as e:
            return []
    except:
        return []
