import threading, json, time, os, phonenumbers, csv, sys
from colorama import init, Fore
from concurrent.futures import ThreadPoolExecutor, as_completed

from src.utils import *
from src.zillow import *
from src.paypal import *
from src.microsoft import *
from src.twitter import *
from src.facebook import *
from src.uber import *

init()  # Initialize colorama

def detect_input_format(filename):
    """Detect if input is CSV or text format"""
    try:
        with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
            first_line = f.readline().strip()
            # Check if it looks like CSV (has commas and typical CSV headers)
            if ',' in first_line and any(header in first_line.lower() for header in ['email', 'phone', 'address', 'name']):
                return 'csv'
            else:
                return 'text'
    except:
        return 'text'

def parse_csv_line(row, csv_format):
    """Parse CSV line based on format configuration"""
    # Default format: email,name,years,address,phones,wealth
    # User can specify custom format like: email,phone,address or email,address,phone

    if not csv_format:
        # Default format
        return {
            'email': row.get('email', ''),
            'name': row.get('name', ''),
            'address': row.get('address', ''),
            'phones': row.get('phones', ''),
            'years': row.get('years', ''),
            'wealth': row.get('wealth', '')
        }

    # Custom format - map columns by position
    columns = [col.strip() for col in csv_format.split(',')]
    result = {'email': '', 'name': '', 'address': '', 'phones': '', 'years': '', 'wealth': ''}

    # If row is a list (from CSV reader), map by position
    if isinstance(row, list):
        for i, col_name in enumerate(columns):
            if i < len(row) and col_name in result:
                result[col_name] = row[i]
    # If row is a dict, map directly
    elif isinstance(row, dict):
        for col_name in columns:
            if col_name in row:
                result[col_name] = row[col_name]

    return result

def run_recovery_module(module_name, scrape_func, email):
    """Run a single recovery module with retries"""
    hints = []
    if not config['recovery'].get(module_name, False):
        return hints

    for i in range(10):
        try:
            if module_name == 'microsoft':
                result = scrape_func(email)
                if isinstance(result, tuple) and len(result) >= 1:
                    hints = result[0] if result[0] else []
                else:
                    hints = result if result else []
            else:
                hints = scrape_func(email)

            # If we got results, break out of retry loop
            if hints:
                break

        except Exception as e:
            error_msg = str(e)
            s_print(f'[{module_name.upper()}] Attempt {i+1}/10 failed: {error_msg}', Fore.RED)
            if i == 9:  # Last attempt
                s_print(f'[{module_name.upper()}] All attempts failed for {email}', Fore.RED)
            continue

    if len(hints) > 0:
        s_print(f"[{module_name.upper()}]: {email} | {hints}", Fore.YELLOW)
    else:
        s_print(f"[{module_name.upper()}]: {email} | No hints found", Fore.CYAN)

    return hints if hints else []

def process_single_entry(entry_data):
    """Process a single entry (email/phone/address combination)"""
    try:
        email = entry_data.get('email', '').strip()
        name = entry_data.get('name', '').strip()
        address = entry_data.get('address', '').strip()
        csv_phones = entry_data.get('phones', '').strip()
        years = entry_data.get('years', '').strip()
        wealth = entry_data.get('wealth', '').strip()

        if not email:
            return None

        s_print(f"Processing: {email}", Fore.CYAN)

        all_hints = []

        # Run all recovery modules if not disabled
        if not config.get("disable-recovery", False):
            # Define recovery modules
            recovery_modules = [
                ('paypal', scrape_paypal),
                ('uber', scrape_uber),
                ('facebook', scrape_facebook),
                ('microsoft', scrape_live),
                ('twitter', scrape_twitter)
            ]

            # Run all modules in parallel using ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_module = {
                    executor.submit(run_recovery_module, module_name, scrape_func, email): module_name
                    for module_name, scrape_func in recovery_modules
                }

                for future in as_completed(future_to_module):
                    module_name = future_to_module[future]
                    try:
                        hints = future.result()
                        if hints:
                            all_hints.extend(hints)
                    except Exception as e:
                        s_print(f'[{module_name.upper()}] Exception: {str(e)}', Fore.RED)

        # Check for phone number matches
        matched_phones = []
        if csv_phones and all_hints:
            matched_phones = phone_number_match(csv_phones, all_hints)

        # Format phone numbers for output
        phone_output = ""
        if matched_phones:
            if config.get('1-number-mode', False):
                phone_output = matched_phones[0]  # Only first match
            else:
                phone_output = ", ".join(matched_phones)
        elif csv_phones:
            # If no matches but we have CSV phones, show them
            if config.get('1-number-mode', False):
                first_phone = csv_phones.split(',')[0].strip() if ',' in csv_phones else csv_phones
                phone_output = first_phone
            else:
                phone_output = csv_phones

        # Get Zillow estimate if enabled
        house_value = ""
        if config['scrape'].get('zestimate', False) and address:
            try:
                s_print(f"Getting Zillow estimate for: {address}", Fore.CYAN)
                house_value = get_house_value(address)
                if house_value is None or house_value == "None":
                    house_value = ""
                s_print(f"Zillow estimate for {address}: {house_value}", Fore.GREEN)
            except Exception as e:
                s_print(f"Zillow error for {address}: {str(e)}", Fore.YELLOW)
                house_value = ""

        # Create CSV row
        csv_row = [
            email if email else "",
            name if name else "",
            address if address else "",
            phone_output if phone_output else "",
            house_value if house_value else ""
        ]

        # Convert to CSV format (escape commas and quotes)
        csv_formatted_row = []
        for field in csv_row:
            field_str = str(field).strip()
            # If field contains comma, quote, or newline, wrap in quotes and escape quotes
            if ',' in field_str or '"' in field_str or '\n' in field_str:
                field_str = '"' + field_str.replace('"', '""') + '"'
            csv_formatted_row.append(field_str)

        final_output = ",".join(csv_formatted_row)

        # Save result
        save(final_output, config['output'])
        s_print(f"Completed: {email} | Matches: {len(matched_phones)} | Zillow: {house_value}", Fore.GREEN)

        return final_output

    except Exception as e:
        s_print(f"Error processing entry: {str(e)}", Fore.RED)
        return None

def main():
    """Main function to run the recovery tool"""
    try:
        # Clear output file and write CSV headers
        output_file = config['output']
        if not output_file.endswith('.csv'):
            output_file = output_file.replace('.txt', '.csv')
            config['output'] = output_file

        with open(config['output'], 'w', encoding='utf-8') as f:
            # Write CSV headers
            headers = "email,name,address,phones,zestimate\n"
            f.write(headers)

        s_print("=== Email Recovery Tool Started ===", Fore.MAGENTA)
        s_print(f"Input file: {config['input']}", Fore.CYAN)
        s_print(f"Output file: {config['output']}", Fore.CYAN)
        s_print(f"Threads: {config['threads']}", Fore.CYAN)
        s_print(f"1-number-mode: {config.get('1-number-mode', False)}", Fore.CYAN)
        s_print(f"Zillow enabled: {config['scrape'].get('zestimate', False)}", Fore.CYAN)

        # Detect input format
        input_format = detect_input_format(config['input'])
        s_print(f"Detected input format: {input_format}", Fore.CYAN)

        entries_to_process = []

        if input_format == 'csv':
            # Read CSV file
            try:
                with open(config['input'], 'r', encoding='utf-8', errors='ignore') as f:
                    # Try to detect if it has headers
                    sample = f.read(1024)
                    f.seek(0)

                    # Check if first line looks like headers
                    first_line = f.readline().strip()
                    f.seek(0)

                    has_headers = any(header in first_line.lower() for header in ['email', 'phone', 'address', 'name'])

                    if has_headers:
                        reader = csv.DictReader(f)
                        for row in reader:
                            parsed = parse_csv_line(row, config.get('csv-format', ''))
                            if parsed.get('email'):
                                entries_to_process.append(parsed)
                    else:
                        # No headers, assume format: email,name,years,address,phones,wealth
                        reader = csv.reader(f)
                        for row in reader:
                            if len(row) >= 1 and row[0].strip():  # At least email
                                parsed = {
                                    'email': row[0].strip() if len(row) > 0 else '',
                                    'name': row[1].strip() if len(row) > 1 else '',
                                    'years': row[2].strip() if len(row) > 2 else '',
                                    'address': row[3].strip() if len(row) > 3 else '',
                                    'phones': row[4].strip() if len(row) > 4 else '',
                                    'wealth': row[5].strip() if len(row) > 5 else ''
                                }
                                entries_to_process.append(parsed)

            except Exception as e:
                s_print(f"Error reading CSV file: {str(e)}", Fore.RED)
                return

        else:
            # Read text file (one email per line)
            try:
                with open(config['input'], 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        email = line.strip()
                        if email and '@' in email:
                            entries_to_process.append({
                                'email': email,
                                'name': '',
                                'address': '',
                                'phones': '',
                                'years': '',
                                'wealth': ''
                            })
            except Exception as e:
                s_print(f"Error reading text file: {str(e)}", Fore.RED)
                return

        if not entries_to_process:
            s_print("No valid entries found in input file", Fore.RED)
            return

        s_print(f"Found {len(entries_to_process)} entries to process", Fore.GREEN)

        # Process entries with threading
        max_threads = min(config.get('threads', 64), len(entries_to_process))
        s_print(f"Using {max_threads} threads", Fore.CYAN)

        processed_count = 0
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            # Submit all tasks
            future_to_entry = {
                executor.submit(process_single_entry, entry): entry
                for entry in entries_to_process
            }

            # Process completed tasks
            for future in as_completed(future_to_entry):
                entry = future_to_entry[future]
                try:
                    result = future.result()
                    processed_count += 1

                    if processed_count % 10 == 0:
                        elapsed = time.time() - start_time
                        rate = processed_count / elapsed if elapsed > 0 else 0
                        s_print(f"Progress: {processed_count}/{len(entries_to_process)} ({rate:.2f}/sec)", Fore.BLUE)

                except Exception as e:
                    s_print(f"Error processing {entry.get('email', 'unknown')}: {str(e)}", Fore.RED)

        elapsed_time = time.time() - start_time
        s_print(f"=== Processing Complete ===", Fore.MAGENTA)
        s_print(f"Processed: {processed_count}/{len(entries_to_process)} entries", Fore.GREEN)
        s_print(f"Time taken: {elapsed_time:.2f} seconds", Fore.GREEN)
        s_print(f"Average rate: {processed_count/elapsed_time:.2f} entries/sec", Fore.GREEN)
        s_print(f"Results saved to: {config['output']}", Fore.GREEN)

    except KeyboardInterrupt:
        s_print("\n=== Interrupted by user ===", Fore.YELLOW)
    except Exception as e:
        s_print(f"Fatal error: {str(e)}", Fore.RED)

if __name__ == "__main__":
    main()


