import threading, json, time, os, phonenumbers
from colorama import init, Fore

from src.utils import *
from src.zillow import *
from src.paypal import *
from src.microsoft import *
from src.twitter import *
from src.facebook import *
from src.uber import *

if config['scrape']['zestimate']:
        house_value = "None"
        try:
            house_value = get_house_value(address)
        except:
            pass

        save(f"{output_txt} | Zestimate: {house_value}", config['output'])
    else:
        save(f"{output_txt}", config['output'])

if not config["disable-recovery"]:

            if config['recovery']['paypal']:
                paypal_hints = []
                for i in range(10):
                    try:
                        paypal_hints = scrape_paypal(email)
                        break
                    except Exception: pass
                        #s_print('[PAYPAL] Retrying...', Fore.RED)
                
                if len(paypal_hints) > 0:
                    s_print(f"[PAYPAL]: {email} | {paypal_hints}", Fore.YELLOW)
                    hints.extend(paypal_hints)

            if config['recovery']['uber']:
                uber_hints = []
                for i in range(10):
                    try:
                        uber_hints = scrape_uber(email)
                        break
                    except Exception: pass
                        #s_print('[UBER] Retrying...', Fore.RED)
                
                if len(uber_hints) > 0:
                    s_print(f"[UBER]: {email} | {uber_hints}", Fore.YELLOW)
                    hints.extend(uber_hints)

            #twitter_hints = []

            if config['recovery']['microsoft']:
                microsoft_hints, two_fa, email_hints_outlook = [], None, []

                # MICROSOFT
                for i in range(10):
                    try:
                        microsoft_hints, two_fa, email_hints_outlook = scrape_live(email)
                        break
                    except Exception:
                        s_print('[MICROSOFT] Retrying...', Fore.RED)

                if len(microsoft_hints) > 0:
                    s_print(f"[MICROSOFT]: {email} | {microsoft_hints}", Fore.YELLOW)
                    hints.extend(microsoft_hints)
            

            # FACEBOOK
            if config['recovery']['facebook']:
                facebook_hints = []
                for i in range(10):
                    try:
                        facebook_hints = scrape_facebook(email)
                        break
                    except Exception as e:
                        s_print('[FACEBOOK] Retrying...', Fore.RED)

                if len(facebook_hints) > 0:
                    s_print(f"[FACEBOOK]: {email} | {facebook_hints}", Fore.YELLOW)
                    hints.extend(facebook_hints)
            

            if config['recovery']['twitter']:
                twitter_hints = []
                for i in range(10):
                    try:
                        twitter_hints = scrape_twitter(email)
                        break
                    except Exception:
                        s_print('[TWITTER] Retrying...', Fore.RED)
                
                if len(twitter_hints) > 0:
                    s_print(f"[TWITTER]: {email} | {twitter_hints}", Fore.YELLOW)
                    hints.extend(twitter_hints)


