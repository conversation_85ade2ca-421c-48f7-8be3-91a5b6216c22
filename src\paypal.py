import re
from curl_cffi import requests
from colorama import Fore, init
from .utils import proxy, s_print

def scrape_paypal(email):
    page = None
    for i in range(10):
        try:
            s = requests.Session(
                impersonate="safari17_2_ios"
            )
            s.proxies = {'http': proxy, 'https': proxy}
            s.headers = {
                'authority': 'www.paypal.com',
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'accept-language': 'en-GB,en;q=0.6',
                'sec-ch-ua': '"Chromium";v="116", "Not)A;Brand";v="24", "Brave";v="116"',
                'sec-ch-ua-mobile': '?0', 
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'sec-gpc': '1',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
            }
            page = s.get(f"https://www.paypal.com/authflow/password-recovery/", impersonate="safari17_2_ios").text
            csrf = re.search(r'content="([^"]+)"\s+name="csrf-token"', page)
            anw_sid = re.search(r'<input\s+type="hidden"\s+name="anw_sid"\s+value="([^"]+)"\s*/>', page)
            if csrf and anw_sid:break
        except Exception as e:
            pass 
    if page == None:
        s_print("Proxy didn't connect in time", Fore.YELLOW)
        return []
    if not csrf:
        s_print("Error with proxy quality", Fore.YELLOW)
        return []
    
    #print(page)
    csrf = csrf.group(1)
    anw_sid = anw_sid.group(1)
    fnetSessionId = s.cookies.get("fn_dt")
    
    #print(fnetSessionId)

    for i in range(10):
        try:
            s.post(
                f"https://c.paypal.com/v1/r/d/b/p1",
                headers={
                    "Accept": "*/*",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Connection": "keep-alive",
                    "Content-Type": "application/json",
                    "Host": "c.paypal.com",
                    "Origin": "https://c.paypal.com",
                    "Referer": "https://c.paypal.com/",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; rv:123.0) Gecko/20100101 Firefox/123.0",
                    "X-Requested-With": "XMLHttpRequest"
                },
                cookies={
                    "fn_dt": fnetSessionId
                },
                json={
                    "appId": "ANW",
                    "correlationId": fnetSessionId,
                    "payload": {
                        "activeXDefined": False,
                        "asynchk": {
                            "o": [
                                "ua",
                                "colorDepth",
                                "width",
                                "tz",
                                "time",
                                "appId",
                                "correlationId",
                                "5"
                            ],
                            "ph2": "44f65e7827adcdfe8ed467be1e40e9b5485b6a7a10610d414988b4c4dd1bafc8"
                        },
                        "dst": True,
                        "flashVersion": {
                            "major": 0,
                            "minor": 0,
                            "release": 0
                        },
                        "hlb": {
                            "chromeWSRT": False,
                            "lgSize": 2,
                            "plgSize": 5,
                            "wd": False
                        },
                        "lst": {
                            "ddi": None,
                            "ddiLst": None,
                            "v": None,
                            "vf": None
                        },
                        "navigator": {
                            "appName": "Netscape",
                            "appVersion": "5.0 (Windows)",
                            "buildID": "20181001000000",
                            "cookieEnabled": True,
                            "igt": True,
                            "language": "en-US",
                            "onLine": True,
                            "oscpu": "Windows NT 10.0; Win64; x64",
                            "platform": "Win32",
                            "product": "Gecko",
                            "productSub": "20100101",
                            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0",
                            "vendor": "",
                            "vendorSub": ""
                        },
                        "pt1": {
                            "cd1": "0.00",
                            "i": "500.00",
                            "ph1": "6714.00",
                            "pp1": "533.00",
                            "sf": "0010",
                            "tb": 3
                        },
                        "referer": "",
                        "rvr": "3.8.0-TP",
                        "screen": {
                            "availHeight": 0,
                            "availWidth": 0,
                            "colorDepth": 24,
                            "height": 0,
                            "pixelDepth": 24,
                            "width": 0
                        },
                        "time": 1730796920629,
                        "trt": False,
                        "tz": 0,
                        "tzName": "UTC",
                        "URL": "https://www.paypal.com/authflow/password-recovery/",
                        "window": {
                            "devicePixelRatio": 1,
                            "innerHeight": 0,
                            "innerWidth": 0,
                            "outerHeight": 0,
                            "outerWidth": 0
                        }
                    }
                },
                impersonate="safari17_2_ios"
            )
            break 
        except:
            pass

    for i in range(5):
        try:
            outcome = s.post(
                f"https://www.paypal.com/authflow/password-recovery",
                headers={
                    "Accept": "*/*",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Connection": "keep-alive",
                    "Content-Type": "application/json",
                    "Host": "www.paypal.com",
                    "Origin": "https://www.paypal.com",
                    "Referer": "https://www.paypal.com/authflow/password-recovery/",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; rv:123.0) Gecko/20100101 Firefox/123.0",
                    "X-Requested-With": "XMLHttpRequest"
                },
                cookies={
                    "fn_dt": fnetSessionId
                },
                json={
                    "email":email,
                    "passkeyEligibleBrowser":False,
                    "_csrf": csrf,
                    "anw_sid":anw_sid,
                    "uvpaa": False,
                    "mediation": True,
                    "isCheckoutFlow": False
                },
                impersonate="safari17_2_ios"
            )

            if 'clientInstanceId' in outcome.text:
                break
        except:
            pass

    #print(outcome.text)

    if 'clientInstanceId' not in outcome.text:
        raise Exception("Missing clientInstanceId")
    client_instance_id = outcome.json()["clientInstanceId"]

    for i in range(10):
        try:
            data = s.get(
                f"https://www.paypal.com/authflow/entry/?clientInstanceId={client_instance_id}&anw_sid={anw_sid}",
                impersonate="safari17_2_ios"
            )
            break 
        except:
            pass
    
    
    hint_dupe_check = []
    hints = re.findall(r'data-value="([^"]+)"', data.text)
    for hint in hints:
        hint = hint.replace('\u202a', '').replace('\u202c',"")
        hint = hint.replace("•","*")
        if hint not in hint_dupe_check and "@" not in hint:
            hint_dupe_check.append(hint)

    return hint_dupe_check