import json
from colorama import Fore
from .utils import proxy, lock, s_print, gen_session

def scrape_live(email):
    hints = [] ; two_fa = None
    email_hints = []
    email_type = email.split("@")[1].split(".")[0]

    for i in range(5):
        try:
            s, user_agent = gen_session()
            s.proxies = {'http': proxy, 'https': proxy}

            data = s.get(
                f"https://account.live.com/ResetPassword.aspx?mn={email}",
                headers={
                    'authority': 'account.live.com',
                    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'accept-language': 'en-GB,en;q=0.9',
                    'cache-control': 'no-cache',
                    'pragma': 'no-cache',
                    'sec-ch-ua': '"Not.A/Brand";v="8", "Chromium";v="114", "Opera GX";v="100"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'document',
                    'sec-fetch-mode': 'navigate',
                    'sec-fetch-site': 'none',
                    'sec-fetch-user': '?1',
                    'upgrade-insecure-requests': '1',
                    'user-agent': user_agent,
                },
            )

            break
        except Exception as e:
            if i == 4:  # Last attempt
                s_print(f"Microsoft: Failed to connect for {email}: {str(e)}", Fore.YELLOW)

    try:
        email_data = json.loads(data.text.split('oProofList":')[1].split("],")[0] + "]")
        two_fa = False
    except Exception as e:
        s_print(f"Microsoft: Failed to parse recovery data for {email}: {str(e)}", Fore.YELLOW)
        return [],None,None
    
    for thing in email_data:
        info = thing["name"]
        if thing["type"] == "Email":
            email_hints.append(info)
        elif thing["type"] == "Sms":
            hints.append(info)
        elif thing["type"] == "SQSA":
            hints.append(f"Security question : {info}")
        elif thing["type"] == "TOTPAuthenticatorV2":
            two_fa = True

    return hints,two_fa,email_hints