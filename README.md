# Email Recovery Tool

A comprehensive email recovery tool that runs multiple recovery modules simultaneously and matches phone numbers from hints.

## Features

- **Multi-module Recovery**: Runs all 5 recovery modules (PayPal, Uber, Facebook, Microsoft, Twitter) simultaneously for each email
- **Phone Number Matching**: Matches phone numbers from recovery hints with CSV data
- **Flexible Input Formats**: Supports both CSV and text file inputs
- **Configurable CSV Format**: Customize column order (email,phone,address), (email,address,phone), etc.
- **1-Number Mode**: Option to output only the first matched phone number
- **Zillow Integration**: Get house value estimates for addresses
- **Multi-threading**: Configurable thread count for parallel processing
- **Real-time Progress**: Live progress updates and statistics

## Configuration (config.json)

```json
{
    "threads": 64,                    // Number of threads for parallel processing
    "input": "input.txt",             // Input file path
    "output": "output.txt",           // Output file path
    "1-number-mode": true,            // Only output first matched phone number
    "disable-recovery": false,        // Disable all recovery modules
    "csv-format": "",                 // Custom CSV format (e.g., "email,phone,address")
    "proxy": "*******************:port",
    "scrape": {
        "zestimate": true             // Enable Zillow house value lookup
    },
    "recovery": {
        "paypal": true,               // Enable PayPal recovery
        "uber": true,                 // Enable Uber recovery
        "facebook": true,             // Enable Facebook recovery
        "microsoft": true,            // Enable Microsoft recovery
        "twitter": true               // Enable Twitter recovery
    }
}
```

## Input Formats

### CSV Format (Recommended)
The tool auto-detects CSV format. Default column order:
```
email,name,years,address,phones,wealth
```

Example:
```csv
email,name,years,address,phones,wealth
<EMAIL>,Brandon M Smith,41,5212 Crestwood Dr Harrisburg PA 17109,"(*************, (*************",85
<EMAIL>,James Russell Samuelson,43,844 9th St #5 Santa Monica CA 90403,"(*************, (*************",11
```

### Custom CSV Format
Set `csv-format` in config.json to specify column order:
```json
"csv-format": "email,phone,address"
```
or
```json
"csv-format": "email,address,phone,name"
```

### Text Format
One email per line:
```
<EMAIL>
<EMAIL>
<EMAIL>
```

## Output Format

The tool outputs results in this format:
```
email | name | address | matched_phones | Zestimate: $value
```

Example output:
```
<EMAIL> | Brandon M Smith | 5212 Crestwood Dr Harrisburg PA 17109 | (************* | Zestimate: 245,000$
<EMAIL> | James Russell Samuelson | 844 9th St #5 Santa Monica CA 90403 | (*************, (************* | Zestimate: 1,200,000$
```

## Phone Number Matching

The tool matches phone numbers from recovery hints with CSV phone data using:
- **Last 4 digits matching**: Common for partial phone reveals (e.g., `***-***-1201` matches `(*************`)
- **Full number matching**: Complete phone number matches
- **Multiple format support**: Handles various phone formats: `(*************`, `************`, `************`, etc.

## How It Works

1. **Input Processing**: Reads and parses input file (CSV or text)
2. **Parallel Recovery**: For each email, runs all 5 recovery modules simultaneously
3. **Phone Matching**: Compares recovery hints with CSV phone numbers
4. **Zillow Lookup**: Gets house value estimates for addresses (if enabled)
5. **Output Generation**: Formats and saves results with matched phone numbers

## Usage

1. Configure `config.json` with your settings
2. Prepare your input file (`input.txt` by default)
3. Run the tool:
   ```bash
   python main.py
   ```

## Recovery Modules

- **PayPal**: Extracts phone hints from PayPal password recovery
- **Uber**: Gets phone hints from Uber account recovery
- **Facebook**: Retrieves phone hints from Facebook password reset
- **Microsoft**: Extracts phone/email hints from Microsoft account recovery
- **Twitter**: Gets phone hints from Twitter password reset

## Performance

- **Multi-threading**: Processes multiple emails simultaneously
- **Parallel Recovery**: All 5 modules run concurrently for each email
- **Progress Tracking**: Real-time updates every 10 processed entries
- **Error Handling**: Continues processing even if individual modules fail

## Example Run

```
> === Email Recovery Tool Started ===
> Input file: input.txt
> Output file: output.txt
> Threads: 64
> 1-number-mode: True
> Detected input format: csv
> Found 6 entries to process
> Using 6 threads
> Processing: <EMAIL>
> [PAYPAL]: <EMAIL> | ['***-***-1201']
> [MICROSOFT]: <EMAIL> | ['***-***-2005']
> Completed: <EMAIL> | Matches: 2
> Progress: 10/6 (1.23/sec)
> === Processing Complete ===
> Processed: 6/6 entries
> Time taken: 45.2 seconds
> Average rate: 0.13 entries/sec
> Results saved to: output.txt
```
